import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/debtor_provider.dart';
import '../models/debtor.dart';
import '../models/debt_item.dart';
import '../models/payment.dart';
import '../widgets/common_widgets.dart';
import '../utils/app_theme.dart';
import 'simple_qr_display_screen.dart';
import '../services/pdf_service.dart';
import 'add_item_screen.dart';
import 'add_payment_screen.dart';
import 'add_debtor_screen.dart';
import 'debt_sharing_screen.dart';
import '../services/smart_notification_service.dart';

import '../widgets/overflow_safe_wrapper.dart';

class DebtorDetailScreen extends StatefulWidget {
  final Debtor debtor;

  const DebtorDetailScreen({super.key, required this.debtor});

  @override
  State<DebtorDetailScreen> createState() => _DebtorDetailScreenState();
}

class _DebtorDetailScreenState extends State<DebtorDetailScreen> {
  bool _itemsExpanded = true;
  bool _paymentsExpanded = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.debtor.name,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () => _generateInvoice(context),
            tooltip: 'طباعة الفاتورة',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed:
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => DebtSharingScreen(debtor: widget.debtor),
                  ),
                ),
            tooltip: 'مشاركة الدين',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _navigateToEditDebtor(context),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'delete':
                  _showDeleteConfirmation(context);
                  break;
                case 'schedule_reminder':
                  _showScheduleReminderDialog(context);
                  break;
                case 'urgent_reminder':
                  _sendUrgentReminder(context);
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'urgent_reminder',
                    child: Row(
                      children: [
                        Icon(
                          Icons.notification_important,
                          color: Colors.orange,
                        ),
                        SizedBox(width: 8),
                        Text('تذكير عاجل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'schedule_reminder',
                    child: Row(
                      children: [
                        Icon(Icons.schedule),
                        SizedBox(width: 8),
                        Text('جدولة تذكير'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف المدين', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Consumer<DebtorProvider>(
        builder: (context, provider, child) {
          final items = provider.getDebtItemsForDebtor(widget.debtor.id);
          final payments = provider.getPaymentsForDebtor(widget.debtor.id);
          final remainingDebt = provider.calculateRemainingDebt(
            widget.debtor.id,
          );
          final totalItems = provider.getTotalItemsForDebtor(widget.debtor.id);
          final totalPayments = provider.getTotalPaymentsForDebtor(
            widget.debtor.id,
          );

          return ResponsiveWrapper(
            child: Column(
              children: [
                // Summary Card
                _buildSummaryCard(
                  context,
                  remainingDebt,
                  totalItems,
                  totalPayments,
                ),

                const SizedBox(height: 16),

                // Items Section
                _buildItemsSection(context, items, provider),

                const SizedBox(height: 16),

                // Payments Section
                _buildPaymentsSection(context, payments, provider),

                const SizedBox(height: 80), // Space for FABs
              ],
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButtons(context),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    double remainingDebt,
    double totalItems,
    double totalPayments,
  ) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_IQ',
      symbol: 'دينار عراقي',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');

    return CustomCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الدين',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: remainingDebt > 0 ? Colors.red[50] : Colors.green[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    remainingDebt > 0 ? Colors.red[200]! : Colors.green[200]!,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ المتبقي:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  currencyFormat.format(remainingDebt),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color:
                        remainingDebt > 0 ? Colors.red[700] : Colors.green[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          _buildSummaryRow(
            context,
            'المبلغ الأصلي:',
            currencyFormat.format(widget.debtor.totalDebt),
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            context,
            'إجمالي العناصر:',
            currencyFormat.format(totalItems),
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            context,
            'إجمالي المدفوعات:',
            currencyFormat.format(totalPayments),
          ),

          const SizedBox(height: 16),

          _buildSummaryRow(
            context,
            'تاريخ الإنشاء:',
            dateFormat.format(widget.debtor.createdAt),
          ),
          const SizedBox(height: 8),
          _buildSummaryRow(
            context,
            'آخر نشاط:',
            dateFormat.format(widget.debtor.lastActivity),
          ),

          // عرض تاريخ التسديد إذا كان محدداً
          if (widget.debtor.dueDate != null) ...[
            const SizedBox(height: 8),
            _buildDueDateRow(context, dateFormat),
          ],

          if (widget.debtor.notes != null &&
              widget.debtor.notes!.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'ملاحظات:',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              widget.debtor.notes!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: Theme.of(context).textTheme.bodyMedium),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildItemsSection(
    BuildContext context,
    List<DebtItem> items,
    DebtorProvider provider,
  ) {
    return CustomCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _itemsExpanded = !_itemsExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Icon(_itemsExpanded ? Icons.expand_less : Icons.expand_more),
                  const SizedBox(width: 8),
                  Text(
                    'العناصر المضافة (${items.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: () => _navigateToAddItem(context),
                    icon: const Icon(Icons.add, size: 18),
                    label: const Text('إضافة'),
                  ),
                ],
              ),
            ),
          ),

          if (_itemsExpanded) ...[
            const Divider(height: 1),
            if (items.isEmpty)
              const Padding(
                padding: EdgeInsets.all(32),
                child: EmptyStateWidget(
                  icon: Icons.shopping_cart_outlined,
                  title: 'لا توجد عناصر',
                  subtitle: 'لم يتم إضافة أي عناصر بعد',
                ),
              )
            else
              ...items.map((item) => _buildItemTile(context, item, provider)),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentsSection(
    BuildContext context,
    List<Payment> payments,
    DebtorProvider provider,
  ) {
    return CustomCard(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _paymentsExpanded = !_paymentsExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  Icon(
                    _paymentsExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'المدفوعات (${payments.length})',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: () => _navigateToAddPayment(context),
                    icon: const Icon(Icons.add, size: 18),
                    label: const Text('إضافة'),
                  ),
                ],
              ),
            ),
          ),

          if (_paymentsExpanded) ...[
            const Divider(height: 1),
            if (payments.isEmpty)
              const Padding(
                padding: EdgeInsets.all(32),
                child: EmptyStateWidget(
                  icon: Icons.payment_outlined,
                  title: 'لا توجد مدفوعات',
                  subtitle: 'لم يتم تسجيل أي مدفوعات بعد',
                ),
              )
            else
              ...payments.map(
                (payment) => _buildPaymentTile(context, payment, provider),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildItemTile(
    BuildContext context,
    DebtItem item,
    DebtorProvider provider,
  ) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_IQ',
      symbol: 'دينار عراقي',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.orange[100],
        child: Icon(Icons.shopping_cart, color: Colors.orange[700]),
      ),
      title: Text(item.itemName),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(dateFormat.format(item.date)),
          if (item.description != null && item.description!.isNotEmpty)
            Text(
              item.description!,
              style: Theme.of(context).textTheme.bodySmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            currencyFormat.format(item.price),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteItemConfirmation(context, item, provider);
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red, size: 20),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentTile(
    BuildContext context,
    Payment payment,
    DebtorProvider provider,
  ) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_IQ',
      symbol: 'دينار عراقي',
      decimalDigits: 0,
    );
    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.green[100],
        child: Icon(Icons.payment, color: Colors.green[700]),
      ),
      title: Text('دفعة - ${payment.method.displayName}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(dateFormat.format(payment.date)),
          if (payment.notes != null && payment.notes!.isNotEmpty)
            Text(
              payment.notes!,
              style: Theme.of(context).textTheme.bodySmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            currencyFormat.format(payment.amount),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green[700],
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeletePaymentConfirmation(context, payment, provider);
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red, size: 20),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        FloatingActionButton(
          heroTag: "generate_qr",
          onPressed: () => _navigateToQRGenerator(context),
          backgroundColor: Colors.purple,
          child: const Icon(Icons.qr_code),
        ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "add_payment",
          onPressed: () => _navigateToAddPayment(context),
          backgroundColor: AppTheme.successColor,
          child: const Icon(Icons.payment),
        ),
        const SizedBox(height: 16),
        FloatingActionButton(
          heroTag: "add_item",
          onPressed: () => _navigateToAddItem(context),
          child: const Icon(Icons.add_shopping_cart),
        ),
      ],
    );
  }

  void _navigateToQRGenerator(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SimpleQRDisplayScreen(debtor: widget.debtor),
      ),
    );
  }

  void _navigateToAddItem(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddItemScreen(debtor: widget.debtor),
      ),
    );
  }

  void _navigateToAddPayment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddPaymentScreen(debtor: widget.debtor),
      ),
    );
  }

  void _navigateToEditDebtor(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddDebtorScreen(debtor: widget.debtor),
      ),
    );
  }

  // بناء صف تاريخ التسديد
  Widget _buildDueDateRow(BuildContext context, DateFormat dateFormat) {
    if (widget.debtor.dueDate == null) return const SizedBox.shrink();

    final now = DateTime.now();
    final dueDate = widget.debtor.dueDate!;
    final isOverdue = now.isAfter(dueDate);
    final daysDifference = dueDate.difference(now).inDays;

    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (isOverdue) {
      statusColor = Colors.red;
      statusIcon = Icons.warning;
      statusText = 'متأخر ${daysDifference.abs()} يوم';
    } else if (daysDifference <= 3) {
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
      statusText =
          daysDifference == 0 ? 'مستحق اليوم' : 'باقي $daysDifference أيام';
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'باقي $daysDifference يوم';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تاريخ التسديد المطلوب:',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  dateFormat.format(dueDate),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  statusText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context) async {
    final confirmed = await ConfirmationDialog.show(
      context,
      title: 'حذف المدين',
      content:
          'هل أنت متأكد من حذف "${widget.debtor.name}"؟\nسيتم حذف جميع العناصر والمدفوعات المرتبطة به.',
      confirmText: 'حذف',
      confirmColor: Colors.red,
    );

    if (confirmed == true && context.mounted) {
      final provider = Provider.of<DebtorProvider>(context, listen: false);
      await provider.deleteDebtor(widget.debtor.id);

      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف "${widget.debtor.name}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showDeleteItemConfirmation(
    BuildContext context,
    DebtItem item,
    DebtorProvider provider,
  ) async {
    final confirmed = await ConfirmationDialog.show(
      context,
      title: 'حذف العنصر',
      content: 'هل أنت متأكد من حذف "${item.itemName}"؟',
      confirmText: 'حذف',
      confirmColor: Colors.red,
    );

    if (confirmed == true && context.mounted) {
      await provider.deleteDebtItem(item.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف "${item.itemName}" بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showDeletePaymentConfirmation(
    BuildContext context,
    Payment payment,
    DebtorProvider provider,
  ) async {
    final confirmed = await ConfirmationDialog.show(
      context,
      title: 'حذف الدفعة',
      content: 'هل أنت متأكد من حذف هذه الدفعة؟',
      confirmText: 'حذف',
      confirmColor: Colors.red,
    );

    if (confirmed == true && context.mounted) {
      await provider.deletePayment(payment.id);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الدفعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showScheduleReminderDialog(BuildContext context) {
    // TODO: Implement reminder scheduling dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة جدولة التذكيرات ستكون متاحة قريباً')),
    );
  }

  Future<void> _sendUrgentReminder(BuildContext context) async {
    try {
      await SmartNotificationService.sendUrgentReminder(widget.debtor.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال تذكير عاجل للمدين: ${widget.debtor.name}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'إعدادات الإشعارات',
              onPressed: () {
                Navigator.of(
                  context,
                ).pushNamed('/smart_notifications_settings');
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال التذكير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _generateInvoice(BuildContext context) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final provider = Provider.of<DebtorProvider>(context, listen: false);
      final items = provider.getDebtItemsForDebtor(widget.debtor.id);
      final payments = provider.getPaymentsForDebtor(widget.debtor.id);

      // Generate PDF
      final pdfBytes = await PDFService.generateInvoicePDF(
        debtor: widget.debtor,
        debtItems: items,
        payments: payments,
      );

      // Check if widget is still mounted
      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      // Generate file name
      final fileName = PDFService.generateInvoiceFileName(widget.debtor.name);

      // Show preview with options
      PDFService.previewPDF(context, pdfBytes, fileName, widget.debtor.name);
    } catch (e) {
      // Check if widget is still mounted
      if (!mounted) return;

      // Close loading dialog if still open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنتاج الفاتورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
