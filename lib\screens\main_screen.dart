import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/debtor_provider.dart';
import '../providers/settings_provider.dart';
import '../utils/app_theme.dart';
import '../widgets/gradient_app_bar.dart';

import 'professional_qr_generator_screen.dart';
import 'professional_qr_scanner_screen.dart';
import '../services/due_date_service.dart';
import 'debtor_list_screen.dart';
import 'settings_screen.dart';
import 'about_screen.dart';
import 'add_debtor_screen.dart';
import '../services/interactive_tutorial_service.dart';
import '../widgets/main_screen_shimmer.dart';
import '../l10n/app_localizations.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  bool _showShimmer = true;

  late final List<Widget> _screens;

  // Global keys for tutorial
  final GlobalKey _homeTabKey = GlobalKey();
  final GlobalKey _statsTabKey = GlobalKey();
  final GlobalKey _addButtonKey = GlobalKey();
  final GlobalKey _drawerButtonKey = GlobalKey();
  final GlobalKey _drawerQRScannerKey = GlobalKey();
  final GlobalKey _drawerSettingsKey = GlobalKey();
  final GlobalKey _drawerBackupKey = GlobalKey();
  final GlobalKey _drawerAboutKey = GlobalKey();
  final GlobalKey _drawerTutorialKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _screens = [const DebtorListScreen(showAppBar: false), const StatsScreen()];

    // فحص تلقائي للديون المتأخرة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 2), () async {
        try {
          if (mounted) {
            await DueDateService.performDailyCheck(context);
          }
        } catch (e) {
          debugPrint('Error performing due date check: $e');
        }
      });
    });

    // Register keys for tutorial
    WidgetsBinding.instance.addPostFrameCallback((_) {
      InteractiveTutorialService.registerWidgetKey('home_tab', _homeTabKey);
      InteractiveTutorialService.registerWidgetKey('stats_tab', _statsTabKey);
      InteractiveTutorialService.registerWidgetKey(
        'add_debtor_button',
        _addButtonKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_button',
        _drawerButtonKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_qr_scanner',
        _drawerQRScannerKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_settings',
        _drawerSettingsKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_backup',
        _drawerBackupKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_about',
        _drawerAboutKey,
      );
      InteractiveTutorialService.registerWidgetKey(
        'drawer_tutorial',
        _drawerTutorialKey,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Scaffold(
          backgroundColor: AppTheme.scaffoldBackgroundColor,
          appBar: GradientAppBar(
            title: _getAppBarTitle(settings),
            gradientColors: AppTheme.primaryGradient,
            leading: Builder(
              builder:
                  (context) => IconButton(
                    key: _drawerButtonKey,
                    icon: const Icon(Icons.menu),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                  ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.qr_code_scanner),
                onPressed: () => _navigateToQRScanner(context),
                tooltip: 'مسح QR',
              ),
              IconButton(
                icon: const Icon(Icons.qr_code),
                onPressed: () => _navigateToQRGenerator(context),
                tooltip: 'إنشاء QR',
              ),
            ],
          ),
          drawer: _buildDrawer(context),
          body: Stack(
            children: [
              IndexedStack(index: _currentIndex, children: _screens),
              if (_showShimmer)
                MainScreenShimmer(
                  onComplete: () {
                    setState(() {
                      _showShimmer = false;
                    });
                  },
                ),
            ],
          ),
          bottomNavigationBar: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.grey.shade50],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.15),
                  blurRadius: 25,
                  offset: const Offset(0, -8),
                ),
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, -3),
                ),
              ],
              border: Border(
                top: BorderSide(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
            ),
            child: SafeArea(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(30),
                ),
                child: Container(
                  constraints: const BoxConstraints(
                    minHeight: 60,
                    maxHeight: 80,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 6,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _buildEnhancedNavItem(
                        icon: Icons.people_outline,
                        activeIcon: Icons.people,
                        label: AppLocalizations.of(context).debtors,
                        index: 0,
                        key: _homeTabKey,
                      ),
                      _buildCenterAddButton(),
                      _buildEnhancedNavItem(
                        icon: Icons.analytics_outlined,
                        activeIcon: Icons.analytics,
                        label: AppLocalizations.of(context).statistics,
                        index: 1,
                        key: _statsTabKey,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _getAppBarTitle(SettingsProvider settings) {
    final l10n = AppLocalizations.of(context);
    switch (_currentIndex) {
      case 0:
        return Text(
          settings.language == AppLanguage.arabic
              ? 'كوزمتك وكماليات باسم'
              : 'Basim Cosmetics',
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        );
      case 1:
        return Text(
          l10n.statisticsTitle,
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        );
      default:
        return Text(
          settings.language == AppLanguage.arabic
              ? 'كوزمتك وكماليات باسم'
              : 'Basim Cosmetics',
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
        );
    }
  }

  Widget _buildEnhancedNavItem({
    required IconData icon,
    required IconData activeIcon,
    required String label,
    required int index,
    GlobalKey? key,
  }) {
    final isSelected = _currentIndex == index;

    return Expanded(
      child: GestureDetector(
        key: key,
        onTap: () {
          setState(() {
            _currentIndex = index;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          decoration:
              isSelected
                  ? BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withValues(alpha: 0.15),
                        AppTheme.primaryColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  )
                  : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isSelected ? activeIcon : icon,
                color:
                    isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: isSelected ? 11 : 10,
                    fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                    color:
                        isSelected
                            ? AppTheme.primaryColor
                            : AppTheme.textSecondaryColor,
                    letterSpacing: isSelected ? 0.3 : 0,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCenterAddButton() {
    return Expanded(
      child: GestureDetector(
        key: _addButtonKey,
        onTap: () {
          // Navigate to add debtor screen
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddDebtorScreen()),
          );
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: AppTheme.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.4),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SizedBox(
            height: 50,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.add_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  AppLocalizations.of(context).add,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    letterSpacing: 0.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade50],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            // Header with gradient
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 30),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Logo/Avatar
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.store_rounded,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // App name
                  Consumer<SettingsProvider>(
                    builder: (context, settings, child) {
                      final l10n = AppLocalizations.of(context);
                      return Column(
                        children: [
                          Text(
                            settings.language == AppLanguage.arabic
                                ? 'كوزمتك باسم'
                                : 'Basim Cosmetics',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w800,
                              letterSpacing: 1,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            l10n.smartDebtManagement,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),

            // Menu items
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Column(
                  children: [
                    Consumer<SettingsProvider>(
                      builder: (context, settings, child) {
                        final l10n = AppLocalizations.of(context);
                        return Column(
                          children: [
                            // زر التبديل بين الوضع الفاتح والداكن
                            _buildDrawerItem(
                              context,
                              icon:
                                  settings.appThemeMode == AppThemeMode.dark
                                      ? Icons.light_mode_rounded
                                      : Icons.dark_mode_rounded,
                              title:
                                  settings.appThemeMode == AppThemeMode.dark
                                      ? (settings.language == AppLanguage.arabic
                                          ? 'الوضع الفاتح'
                                          : 'Light Mode')
                                      : (settings.language == AppLanguage.arabic
                                          ? 'الوضع الداكن'
                                          : 'Dark Mode'),
                              subtitle:
                                  settings.language == AppLanguage.arabic
                                      ? 'تغيير مظهر التطبيق'
                                      : 'Change app appearance',
                              onTap: () {
                                settings.toggleTheme();
                              },
                            ),
                            _buildDrawerItem(
                              context,
                              icon: Icons.settings_rounded,
                              title: l10n.settings,
                              subtitle: l10n.appSettings,
                              key: _drawerSettingsKey,
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => const SettingsScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildDrawerItem(
                              context,
                              icon: Icons.info_rounded,
                              title: l10n.aboutApp,
                              subtitle: l10n.appInformation,
                              key: _drawerAboutKey,
                              onTap: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const AboutScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildDrawerItem(
                              context,
                              icon: Icons.school_rounded,
                              title: l10n.tutorial,
                              subtitle: l10n.howToUse,
                              key: _drawerTutorialKey,
                              onTap: () {
                                Navigator.pop(context);
                                InteractiveTutorialService.startInteractiveTutorial(
                                  context,
                                );
                              },
                            ),
                            _buildDrawerItem(
                              context,
                              icon: Icons.help_rounded,
                              title:
                                  settings.language == AppLanguage.arabic
                                      ? 'المساعدة'
                                      : 'Help',
                              subtitle:
                                  settings.language == AppLanguage.arabic
                                      ? 'دليل الاستخدام والدعم'
                                      : 'User guide and support',
                              onTap: () {
                                Navigator.pop(context);
                                // TODO: Add help screen
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

            // Footer
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Divider(color: Colors.grey.shade300),
                  const SizedBox(height: 10),
                  Text(
                    'الإصدار 1.0.0',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  Text(
                    '© 2024 كوزمتك باسم',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Key? key,
  }) {
    return Container(
      key: key,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          splashColor: AppTheme.primaryColor.withValues(alpha: 0.1),
          highlightColor: AppTheme.primaryColor.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade200, width: 1),
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withValues(alpha: 0.1),
                        AppTheme.primaryColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: Center(
                    child: Icon(icon, color: AppTheme.primaryColor, size: 24),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: AppTheme.textSecondaryColor,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToQRScanner(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfessionalQRScannerScreen(),
      ),
    );
  }

  void _navigateToQRGenerator(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfessionalQRGeneratorScreen(),
      ),
    );
  }
}

class StatsScreen extends StatelessWidget {
  const StatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DebtorProvider>(
      builder: (context, provider, child) {
        final totalDebtors = provider.debtors.length;
        final totalItems = provider.debtItems.length;
        final totalPayments = provider.payments.length;

        final totalDebt = provider.debtors.fold<double>(
          0.0,
          (sum, debtor) => sum + provider.calculateRemainingDebt(debtor.id),
        );

        final totalItemsValue = provider.debtItems.fold<double>(
          0.0,
          (sum, item) => sum + item.price,
        );

        final totalPaymentsValue = provider.payments.fold<double>(
          0.0,
          (sum, payment) => sum + payment.amount,
        );

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Professional Overview Section
              Container(
                margin: const EdgeInsets.only(bottom: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section header
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: AppTheme.primaryGradient,
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryColor.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.dashboard_rounded,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'نظرة عامة',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppTheme.textPrimaryColor,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'ملخص سريع للحسابات والمعاملات',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Stats cards with professional layout
                    Row(
                      children: [
                        Expanded(
                          child: _buildEnhancedStatCard(
                            context,
                            'المدينون',
                            totalDebtors.toString(),
                            Icons.people_rounded,
                            AppTheme.primaryColor,
                            'عدد العملاء',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildEnhancedStatCard(
                            context,
                            'العناصر',
                            totalItems.toString(),
                            Icons.inventory_2_rounded,
                            AppTheme.secondaryColor,
                            'إجمالي العناصر',
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: _buildEnhancedStatCard(
                            context,
                            'المدفوعات',
                            totalPayments.toString(),
                            Icons.payment_rounded,
                            AppTheme.successColor,
                            'عدد المدفوعات',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildEnhancedStatCard(
                            context,
                            'المتبقي',
                            totalDebt.toStringAsFixed(0),
                            Icons.account_balance_wallet_rounded,
                            totalDebt > 0
                                ? AppTheme.warningColor
                                : AppTheme.successColor,
                            'دينار عراقي',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Financial Summary
              _buildFinancialSummaryCard(
                context,
                totalItemsValue,
                totalPaymentsValue,
                totalDebt,
              ),

              const SizedBox(height: 24),

              // Recent Activity
              if (provider.debtors.isNotEmpty)
                _buildRecentActivityCard(context, provider),

              const SizedBox(height: 24),

              // Quick Actions
              _buildQuickActionsCard(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEnhancedStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.grey.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon with gradient background
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [color, color.withValues(alpha: 0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(child: Icon(icon, color: Colors.white, size: 24)),
            ),

            const SizedBox(height: 16),

            // Value with professional styling
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w800,
                color: color,
                letterSpacing: 0.5,
              ),
            ),

            const SizedBox(height: 4),

            // Title
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 2),

            // Subtitle
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummaryCard(
    BuildContext context,
    double totalItems,
    double totalPayments,
    double remainingDebt,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.analytics,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'الملخص المالي',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            _buildFinancialRow(
              context,
              'إجمالي قيمة العناصر:',
              '${totalItems.toStringAsFixed(0)} دينار عراقي',
            ),
            const SizedBox(height: 8),
            _buildFinancialRow(
              context,
              'إجمالي المدفوعات:',
              '${totalPayments.toStringAsFixed(0)} دينار عراقي',
            ),
            const Divider(height: 24),
            _buildFinancialRow(
              context,
              'المبلغ المتبقي:',
              '${remainingDebt.toStringAsFixed(0)} دينار عراقي',
              isTotal: true,
              valueColor:
                  remainingDebt > 0
                      ? AppTheme.errorColor
                      : AppTheme.successColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: valueColor ?? Colors.black,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivityCard(
    BuildContext context,
    DebtorProvider provider,
  ) {
    final recentDebtors = provider.debtors.take(3).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.history,
                    color: AppTheme.secondaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'النشاط الأخير',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.secondaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            ...recentDebtors.map((debtor) {
              final remainingDebt = provider.calculateRemainingDebt(debtor.id);
              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    debtor.name[0],
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(debtor.name),
                subtitle: Text(
                  'المتبقي: ${remainingDebt.toStringAsFixed(0)} دينار عراقي',
                ),
                trailing: Icon(
                  remainingDebt > 0 ? Icons.trending_up : Icons.check_circle,
                  color:
                      remainingDebt > 0
                          ? AppTheme.warningColor
                          : AppTheme.successColor,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.flash_on,
                    color: AppTheme.successColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إجراءات سريعة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to add debtor screen
                      // TODO: Implement navigation to add debtor screen
                    },
                    icon: const Icon(Icons.person_add),
                    label: const Text('إضافة مدين'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AboutScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.info),
                    label: const Text('حول التطبيق'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
