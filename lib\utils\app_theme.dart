import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  // New Color Scheme - Based on user requirements
  // Light Mode Colors
  static const Color primaryColor = Color(0xFF169976); // Green primary
  static const Color primaryLightColor = Color(0xFF1DCD9F); // Light green
  static const Color primaryDarkColor = Color(0xFF0F6B4A); // Dark green
  static const Color secondaryColor = Color(0xFF222222); // Dark gray
  static const Color secondaryLightColor = Color(0xFF444444); // Medium gray
  static const Color errorColor = Color(0xFFDC3545); // Red
  static const Color successColor = Color(0xFF169976); // Same as primary
  static const Color warningColor = Color(0xFFFFC107); // Yellow
  static const Color infoColor = Color(0xFF1DCD9F); // Light green

  // Light Mode Colors
  static const Color lightSurfaceColor = Color(0xFFFFFFFF); // Pure white
  static const Color lightBackgroundColor = Color(
    0xFFF8F9FA,
  ); // Very light gray
  static const Color lightCardColor = Color(0xFFFFFFFF); // Pure white
  static const Color lightScaffoldBackgroundColor = Color(
    0xFFF5F5F5,
  ); // Light gray
  static const Color lightTextColor = Color(0xFF000000); // Pure black
  static const Color lightTextSecondaryColor = Color(0xFF222222); // Dark gray

  // Dark Mode Colors - Sophisticated dark theme
  static const Color darkPrimaryColor = Color(
    0xFF1DCD9F,
  ); // Light green for dark mode
  static const Color darkSecondaryColor = Color(0xFF4A90E2); // Blue accent
  static const Color darkSurfaceColor = Color(0xFF1E1E1E); // Dark surface
  static const Color darkBackgroundColor = Color(
    0xFF121212,
  ); // Very dark background
  static const Color darkCardColor = Color(0xFF2D2D2D); // Dark card
  static const Color darkScaffoldBackgroundColor = Color(
    0xFF0F0F0F,
  ); // Almost black
  static const Color darkTextColor = Color(0xFFFFFFFF); // Pure white text
  static const Color darkTextSecondaryColor = Color(
    0xFFB0B0B0,
  ); // Light gray text

  // Modern Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF169976), // Green primary
    Color(0xFF1DCD9F), // Light green
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF222222), // Dark gray
    Color(0xFF444444), // Medium gray
  ];

  static const List<Color> modernGradient = [
    Color(0xFF169976), // Green primary
    Color(0xFF1DCD9F), // Light green
    Color(0xFF222222), // Dark gray
  ];

  // Typography Scale - Consistent Font Sizes
  static const double fontSizeXSmall = 10.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeRegular = 14.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;
  static const double fontSizeTitle = 28.0;
  static const double fontSizeHeading = 32.0;

  // Icon Sizes - Consistent Icon Scale
  static const double iconSizeXSmall = 16.0;
  static const double iconSizeSmall = 20.0;
  static const double iconSizeRegular = 24.0;
  static const double iconSizeMedium = 28.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 40.0;
  static const double iconSizeXXLarge = 48.0;

  // Spacing Scale - Consistent Spacing
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingRegular = 12.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 20.0;
  static const double spacingXLarge = 24.0;
  static const double spacingXXLarge = 32.0;

  // Border Radius Scale
  static const double radiusSmall = 8.0;
  static const double radiusRegular = 12.0;
  static const double radiusMedium = 16.0;
  static const double radiusLarge = 20.0;
  static const double radiusXLarge = 24.0;

  // Elevation Scale
  static const double elevationSmall = 2.0;
  static const double elevationRegular = 4.0;
  static const double elevationMedium = 8.0;
  static const double elevationLarge = 12.0;
  static const double elevationXLarge = 16.0;

  // Compatibility aliases for existing code
  static Color get surfaceColor => lightSurfaceColor;
  static Color get backgroundColor => lightBackgroundColor;
  static Color get cardColor => lightCardColor;
  static Color get scaffoldBackgroundColor => lightScaffoldBackgroundColor;

  static const List<Color> successGradient = [
    Color(0xFF51CF66),
    Color(0xFF40E0D0),
  ];

  static const List<Color> warningGradient = [
    Color(0xFFFFD93D),
    Color(0xFFFF6B6B),
  ];

  // Luxury Gradients
  static const List<Color> goldGradient = [
    Color(0xFFD4AF37), // Classic Gold
    Color(0xFFFFD700), // Bright Gold
    Color(0xFFB8860B), // Dark Gold
  ];

  static const List<Color> blackGoldGradient = [
    Color(0xFF000000), // Pure Black
    Color(0xFFD4AF37), // Classic Gold
    Color(0xFF764BA2),
  ];

  static const List<Color> twilightGradient = [
    Color(0xFF667EEA),
    Color(0xFF764BA2),
  ];

  // Modern Text Colors - High Contrast & Readable
  static const Color textPrimaryColor = Color(0xFF1E293B); // Dark Slate
  static const Color textSecondaryColor = Color(0xFF64748B); // Slate Gray
  static const Color textHintColor = Color(0xFF94A3B8); // Light Slate
  static const Color textOnPrimaryColor = Colors.white;

  // Card colors
  static const Color cardShadowColor = Color(0x1F000000);

  // Create light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
        surface: lightSurfaceColor,
        error: errorColor,
        primary: primaryColor,
        secondary: secondaryColor,
      ),

      scaffoldBackgroundColor: lightScaffoldBackgroundColor,

      // Elegant App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: elevationSmall,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: 0.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(radiusLarge),
          ),
        ),
        toolbarHeight: 64,
        iconTheme: const IconThemeData(
          color: Colors.white,
          size: iconSizeRegular,
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
      ),

      // Elegant Card Theme
      cardTheme: CardThemeData(
        color: lightCardColor,
        elevation: elevationRegular,
        shadowColor: primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
        margin: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingSmall,
        ),
        surfaceTintColor: Colors.transparent,
        clipBehavior: Clip.antiAlias,
      ),

      // Elegant Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: elevationRegular,
          shadowColor: primaryColor.withValues(alpha: 0.3),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingXLarge,
            vertical: spacingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusRegular),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
          minimumSize: const Size(120, 48),
        ),
      ),

      // Elegant Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingMedium,
            vertical: spacingSmall,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusSmall),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeRegular,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Elegant Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor, width: 1.5),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingLarge,
            vertical: spacingRegular,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusRegular),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: TextStyle(color: Colors.grey[600]),
        hintStyle: TextStyle(color: Colors.grey[400]),
      ),

      // Elegant Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: secondaryColor,
        foregroundColor: Colors.white,
        elevation: elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(radiusMedium)),
        ),
        sizeConstraints: const BoxConstraints.tightFor(width: 56, height: 56),
      ),

      // Elegant List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingSmall,
        ),
        titleTextStyle: const TextStyle(
          fontSize: fontSizeMedium,
          fontWeight: FontWeight.w500,
          color: lightTextColor,
        ),
        subtitleTextStyle: const TextStyle(
          fontSize: fontSizeRegular,
          color: lightTextSecondaryColor,
        ),
        iconColor: primaryColor,
      ),

      // Elegant Divider Theme
      dividerTheme: DividerThemeData(
        color: lightTextSecondaryColor.withValues(alpha: 0.2),
        thickness: 1,
        space: 1,
      ),

      // Elegant Icon Theme
      iconTheme: const IconThemeData(
        color: lightTextSecondaryColor,
        size: iconSizeRegular,
      ),

      // Primary Icon Theme
      primaryIconTheme: const IconThemeData(
        color: textOnPrimaryColor,
        size: 24,
      ),

      // Text Theme
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: textPrimaryColor,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: textSecondaryColor,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: textPrimaryColor,
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: textSecondaryColor,
        ),
      ),
    );
  }

  // Dark Theme Error and Status Colors
  static const Color darkErrorColor = Color(0xFFFF8A80);
  static const Color darkSuccessColor = Color(0xFF1DCD9F); // Use our green
  static const Color darkWarningColor = Color(0xFFFFD54F);
  static const Color darkTextOnPrimaryColor = Color(0xFF0F172A);

  // Create dark theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: darkPrimaryColor,
        brightness: Brightness.dark,
        surface: darkSurfaceColor,
        error: darkErrorColor,
        primary: darkPrimaryColor,
        secondary: darkSecondaryColor,
      ),
      scaffoldBackgroundColor: darkScaffoldBackgroundColor,

      // Dark App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: darkPrimaryColor,
        foregroundColor: darkTextColor,
        elevation: elevationSmall,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: fontSizeXLarge,
          fontWeight: FontWeight.w600,
          color: darkTextColor,
          letterSpacing: 0.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(radiusLarge),
          ),
        ),
        toolbarHeight: 64,
        iconTheme: IconThemeData(color: darkTextColor, size: iconSizeRegular),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
      ),

      // Dark Card Theme
      cardTheme: CardThemeData(
        color: darkCardColor,
        elevation: 8,
        shadowColor: Colors.black.withValues(alpha: 0.4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        surfaceTintColor: Colors.transparent,
        clipBehavior: Clip.antiAlias,
      ),

      // Dark Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: darkPrimaryColor,
          foregroundColor: darkTextOnPrimaryColor,
          elevation: 6,
          shadowColor: darkPrimaryColor.withValues(alpha: 0.3),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            letterSpacing: 0.5,
          ),
          minimumSize: const Size(140, 52),
        ),
      ),

      // Dark Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: darkPrimaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ),

      // Dark Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[600]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[600]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: darkPrimaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: darkErrorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: TextStyle(color: darkTextSecondaryColor),
        hintStyle: TextStyle(color: Colors.grey[500]),
      ),

      // Dark Text Theme
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w400,
          color: darkTextColor,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w400,
          color: darkTextColor,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w400,
          color: darkTextColor,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: darkTextColor,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: darkTextColor,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: darkTextColor,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: darkTextColor,
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: darkTextColor,
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: darkTextColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: darkTextColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: darkTextColor,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: darkTextSecondaryColor,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: darkTextColor,
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: darkTextColor,
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: darkTextSecondaryColor,
        ),
      ),
    );
  }
}
