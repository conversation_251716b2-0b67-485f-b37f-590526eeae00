import 'package:flutter/material.dart';
import 'app_theme.dart';

/// نظام التصميم الموحد للتطبيق
/// يحتوي على جميع الألوان والأحجام والأنماط المستخدمة
class DesignSystem {
  // منع إنشاء كائن من هذا الكلاس
  DesignSystem._();

  /// الألوان الأساسية
  static const Color primary = AppTheme.primaryColor;
  static const Color primaryLight = AppTheme.primaryLightColor;
  static const Color primaryDark = AppTheme.primaryDarkColor;
  static const Color secondary = AppTheme.secondaryColor;
  static const Color secondaryLight = AppTheme.secondaryLightColor;

  /// ألوان الحالة
  static const Color success = AppTheme.successColor;
  static const Color error = AppTheme.errorColor;
  static const Color warning = AppTheme.warningColor;
  static const Color info = AppTheme.infoColor;

  /// أحجام الخطوط
  static const double fontXSmall = AppTheme.fontSizeXSmall;
  static const double fontSmall = AppTheme.fontSizeSmall;
  static const double fontRegular = AppTheme.fontSizeRegular;
  static const double fontMedium = AppTheme.fontSizeMedium;
  static const double fontLarge = AppTheme.fontSizeLarge;
  static const double fontXLarge = AppTheme.fontSizeXLarge;
  static const double fontXXLarge = AppTheme.fontSizeXXLarge;
  static const double fontTitle = AppTheme.fontSizeTitle;
  static const double fontHeading = AppTheme.fontSizeHeading;

  /// أحجام الأيقونات
  static const double iconXSmall = AppTheme.iconSizeXSmall;
  static const double iconSmall = AppTheme.iconSizeSmall;
  static const double iconRegular = AppTheme.iconSizeRegular;
  static const double iconMedium = AppTheme.iconSizeMedium;
  static const double iconLarge = AppTheme.iconSizeLarge;
  static const double iconXLarge = AppTheme.iconSizeXLarge;
  static const double iconXXLarge = AppTheme.iconSizeXXLarge;

  /// المسافات
  static const double spaceXSmall = AppTheme.spacingXSmall;
  static const double spaceSmall = AppTheme.spacingSmall;
  static const double spaceRegular = AppTheme.spacingRegular;
  static const double spaceMedium = AppTheme.spacingMedium;
  static const double spaceLarge = AppTheme.spacingLarge;
  static const double spaceXLarge = AppTheme.spacingXLarge;
  static const double spaceXXLarge = AppTheme.spacingXXLarge;

  /// نصف أقطار الحدود
  static const double radiusSmall = AppTheme.radiusSmall;
  static const double radiusRegular = AppTheme.radiusRegular;
  static const double radiusMedium = AppTheme.radiusMedium;
  static const double radiusLarge = AppTheme.radiusLarge;
  static const double radiusXLarge = AppTheme.radiusXLarge;

  /// الارتفاعات (الظلال)
  static const double elevationSmall = AppTheme.elevationSmall;
  static const double elevationRegular = AppTheme.elevationRegular;
  static const double elevationMedium = AppTheme.elevationMedium;
  static const double elevationLarge = AppTheme.elevationLarge;
  static const double elevationXLarge = AppTheme.elevationXLarge;

  /// أنماط النصوص الجاهزة
  static const TextStyle headingStyle = TextStyle(
    fontSize: fontHeading,
    fontWeight: FontWeight.bold,
    color: AppTheme.lightTextColor,
  );

  static const TextStyle titleStyle = TextStyle(
    fontSize: fontTitle,
    fontWeight: FontWeight.w600,
    color: AppTheme.lightTextColor,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: fontLarge,
    fontWeight: FontWeight.w500,
    color: AppTheme.lightTextColor,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontSize: fontMedium,
    fontWeight: FontWeight.normal,
    color: AppTheme.lightTextColor,
  );

  static const TextStyle captionStyle = TextStyle(
    fontSize: fontSmall,
    fontWeight: FontWeight.normal,
    color: AppTheme.lightTextSecondaryColor,
  );

  /// أنماط الأزرار الجاهزة
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: primary,
    foregroundColor: Colors.white,
    elevation: elevationRegular,
    padding: const EdgeInsets.symmetric(
      horizontal: spaceLarge,
      vertical: spaceMedium,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusRegular),
    ),
    textStyle: const TextStyle(
      fontSize: fontMedium,
      fontWeight: FontWeight.w600,
    ),
  );

  static ButtonStyle get secondaryButtonStyle => OutlinedButton.styleFrom(
    foregroundColor: primary,
    side: BorderSide(color: primary, width: 1.5),
    padding: const EdgeInsets.symmetric(
      horizontal: spaceLarge,
      vertical: spaceMedium,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radiusRegular),
    ),
    textStyle: const TextStyle(
      fontSize: fontMedium,
      fontWeight: FontWeight.w500,
    ),
  );

  /// أنماط الكروت الجاهزة
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: AppTheme.lightCardColor,
    borderRadius: BorderRadius.circular(radiusMedium),
    boxShadow: [
      BoxShadow(
        color: primary.withValues(alpha: 0.1),
        blurRadius: elevationRegular,
        offset: const Offset(0, 2),
      ),
    ],
  );

  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: AppTheme.lightCardColor,
    borderRadius: BorderRadius.circular(radiusLarge),
    boxShadow: [
      BoxShadow(
        color: primary.withValues(alpha: 0.15),
        blurRadius: elevationMedium,
        offset: const Offset(0, 4),
      ),
    ],
  );

  /// التدرجات الجاهزة
  static const LinearGradient primaryGradient = LinearGradient(
    colors: AppTheme.primaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: AppTheme.secondaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient modernGradient = LinearGradient(
    colors: AppTheme.modernGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// الحصول على اللون حسب السياق (فاتح/داكن)
  static Color getTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.darkTextColor
        : AppTheme.lightTextColor;
  }

  static Color getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.darkTextSecondaryColor
        : AppTheme.lightTextSecondaryColor;
  }

  static Color getCardColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.darkCardColor
        : AppTheme.lightCardColor;
  }

  static Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.darkBackgroundColor
        : AppTheme.lightBackgroundColor;
  }

  /// أنماط الإدخال الجاهزة
  static InputDecoration getInputDecoration({
    required String label,
    String? hint,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
  }) {
    return InputDecoration(
      labelText: label,
      hintText: hint,
      prefixIcon:
          prefixIcon != null ? Icon(prefixIcon, size: iconRegular) : null,
      suffixIcon:
          suffixIcon != null
              ? IconButton(
                icon: Icon(suffixIcon, size: iconRegular),
                onPressed: onSuffixTap,
              )
              : null,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusRegular),
        borderSide: BorderSide(color: primary.withValues(alpha: 0.3)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusRegular),
        borderSide: BorderSide(color: primary.withValues(alpha: 0.3)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusRegular),
        borderSide: BorderSide(color: primary, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: spaceMedium,
        vertical: spaceMedium,
      ),
    );
  }

  /// أنماط الحاويات الجاهزة
  static Container buildCard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? elevation,
  }) {
    return Container(
      padding: padding ?? const EdgeInsets.all(spaceMedium),
      margin: margin ?? const EdgeInsets.all(spaceSmall),
      decoration:
          elevation != null && elevation > elevationRegular
              ? elevatedCardDecoration
              : cardDecoration,
      child: child,
    );
  }

  /// أنماط الأيقونات الجاهزة
  static Widget buildIcon({
    required IconData icon,
    double? size,
    Color? color,
    VoidCallback? onTap,
  }) {
    final iconWidget = Icon(
      icon,
      size: size ?? iconRegular,
      color: color ?? primary,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(radiusSmall),
        child: Padding(
          padding: const EdgeInsets.all(spaceSmall),
          child: iconWidget,
        ),
      );
    }

    return iconWidget;
  }
}
